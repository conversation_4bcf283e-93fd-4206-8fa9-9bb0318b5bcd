package com.org.panaroma.commons.enums;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import com.org.panaroma.commons.dto.PaymentSystemEnum;
import org.junit.Test;

/**
 * Test cases for OMS Payment Method Enum including MGV support
 */
public class OmsPaymentMethodEnumTest {

	@Test
	public void testMgvPaymentMethodExists() {
		OmsPaymentMethodEnum mgv = OmsPaymentMethodEnum.MGV;

		assertNotNull(mgv);
		assertEquals("MGV", mgv.getPaymentMethodValue());
		assertEquals("Merchant Gift Voucher", mgv.getPaymentMethodDesc());
		assertEquals(PaymentSystemEnum.MGV, mgv.getUthMappedPaymentStatus());
	}

	@Test
	public void testAllPaymentMethodsHaveValidPaymentSystems() {
		for (OmsPaymentMethodEnum paymentMethod : OmsPaymentMethodEnum.values()) {
			assertNotNull(paymentMethod.getPaymentMethodValue());
			assertNotNull(paymentMethod.getPaymentMethodDesc());
			assertNotNull(paymentMethod.getUthMappedPaymentStatus());
		}
	}

	@Test
	public void testMgvPaymentMethodMapping() {
		// Test that MGV maps to correct payment system
		assertEquals(PaymentSystemEnum.MGV, OmsPaymentMethodEnum.MGV.getUthMappedPaymentStatus());
		assertEquals(6, PaymentSystemEnum.MGV.getPaymentSystemKey().intValue());
	}

	@Test
	public void testGetPaymentMethodByKey() {
		OmsPaymentMethodEnum mgv = null;
		for (OmsPaymentMethodEnum method : OmsPaymentMethodEnum.values()) {
			if ("MGV".equals(method.getPaymentMethodValue())) {
				mgv = method;
				break;
			}
		}

		assertNotNull(mgv);
		assertEquals(OmsPaymentMethodEnum.MGV, mgv);
	}

}
