package com.org.panaroma.commons.enums;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

import com.org.panaroma.commons.dto.PaymentSystemEnum;
import org.junit.Test;

/**
 * Test cases for OMS Refund Payment Method Enum including MGV support
 */
public class OmsRefundPaymentMethodEnumTest {

	@Test
	public void testMgvRefundPaymentMethodExists() {
		OmsRefundPaymentMethodEnum mgv = OmsRefundPaymentMethodEnum.MGV;

		assertNotNull(mgv);
		assertEquals("MGV", mgv.getPaymentMethodValue());
		assertEquals("Merchant Gift Voucher", mgv.getPaymentMethodDesc());
		assertEquals(PaymentSystemEnum.MGV, mgv.getUthMappedPaymentStatus());
	}

	@Test
	public void testAllRefundPaymentMethodsHaveValidPaymentSystems() {
		for (OmsRefundPaymentMethodEnum paymentMethod : OmsRefundPaymentMethodEnum.values()) {
			assertNotNull(paymentMethod.getPaymentMethodValue());
			assertNotNull(paymentMethod.getPaymentMethodDesc());
			assertNotNull(paymentMethod.getUthMappedPaymentStatus());
		}
	}

	@Test
	public void testMgvRefundPaymentMethodMapping() {
		// Test that MGV refund maps to correct payment system
		assertEquals(PaymentSystemEnum.MGV, OmsRefundPaymentMethodEnum.MGV.getUthMappedPaymentStatus());
		assertEquals(6, PaymentSystemEnum.MGV.getPaymentSystemKey().intValue());
	}

	@Test
	public void testGetRefundPaymentMethodByKey() {
		OmsRefundPaymentMethodEnum mgv = null;
		for (OmsRefundPaymentMethodEnum method : OmsRefundPaymentMethodEnum.values()) {
			if ("MGV".equals(method.getPaymentMethodValue())) {
				mgv = method;
				break;
			}
		}

		assertNotNull(mgv);
		assertEquals(OmsRefundPaymentMethodEnum.MGV, mgv);
	}

}
