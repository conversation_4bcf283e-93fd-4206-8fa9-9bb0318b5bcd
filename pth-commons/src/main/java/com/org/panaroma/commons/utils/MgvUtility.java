package com.org.panaroma.commons.utils;

import static com.org.panaroma.commons.constants.WebConstants.MGV_PURCHASED;
import static com.org.panaroma.commons.constants.WebConstants.MGV_REDEEM;
import static com.org.panaroma.commons.constants.WebConstants.TXN_PURPOSE;

import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;

/**
 * Utility class for MGV (Merchant Gift Voucher) specific operations
 */
@Log4j2
public class MgvUtility {

	/**
	 * Checks if the transaction is an MGV purchase
	 */
	public static boolean isMgvPurchase(final TransformedTransactionHistoryDetail txn) {
		if (txn == null) {
			return false;
		}

		TransformedParticipant userParticipant = getUserParticipant(txn);
		if (userParticipant != null && userParticipant.getContextMap() != null) {
			return MGV_PURCHASED.equals(userParticipant.getContextMap().get(TXN_PURPOSE));
		}

		return false;
	}

	/**
	 * Checks if the transaction is an MGV redemption
	 */
	public static boolean isMgvRedemption(final TransformedTransactionHistoryDetail txn) {
		if (txn == null) {
			return false;
		}

		TransformedParticipant userParticipant = getUserParticipant(txn);
		if (userParticipant != null && userParticipant.getContextMap() != null) {
			return MGV_REDEEM.equals(userParticipant.getContextMap().get(TXN_PURPOSE));
		}

		return false;
	}

	/**
	 * Checks if the transaction involves MGV payment system
	 */
	public static boolean isMgvTransaction(final TransformedTransactionHistoryDetail txn) {
		if (txn == null || txn.getParticipants() == null) {
			return false;
		}

		return txn.getParticipants()
			.stream()
			.anyMatch(
					participant -> PaymentSystemEnum.MGV.getPaymentSystemKey().equals(participant.getPaymentSystem()));
	}

	/**
	 * Aggregates multiple MGV transactions for the same order/transaction ID This handles
	 * the case where multiple vouchers are redeemed in a single transaction
	 */
	public static TransformedTransactionHistoryDetail aggregateMgvTransactions(
			final List<TransformedTransactionHistoryDetail> mgvTransactions) {

		if (mgvTransactions == null || mgvTransactions.isEmpty()) {
			return null;
		}

		if (mgvTransactions.size() == 1) {
			return mgvTransactions.get(0);
		}

		// Use the first transaction as the base
		TransformedTransactionHistoryDetail aggregatedTxn = mgvTransactions.get(0);

		// Calculate total amount from all MGV transactions
		long totalAmount = mgvTransactions.stream()
			.mapToLong(txn -> txn.getAmount() != null ? txn.getAmount() : 0L)
			.sum();

		aggregatedTxn.setAmount(totalAmount);

		// Aggregate MGV participant amounts
		aggregateMgvParticipants(aggregatedTxn, mgvTransactions);

		// Add context to indicate this is an aggregated transaction
		if (aggregatedTxn.getContextMap() == null) {
			aggregatedTxn.setContextMap(new java.util.HashMap<>());
		}
		aggregatedTxn.getContextMap().put("mgv_aggregated", "true");
		aggregatedTxn.getContextMap().put("mgv_voucher_count", String.valueOf(mgvTransactions.size()));

		log.info("Aggregated {} MGV transactions into single transaction with total amount: {}", mgvTransactions.size(),
				totalAmount);

		return aggregatedTxn;
	}

	/**
	 * Aggregates MGV participants from multiple transactions
	 */
	private static void aggregateMgvParticipants(final TransformedTransactionHistoryDetail aggregatedTxn,
			final List<TransformedTransactionHistoryDetail> mgvTransactions) {

		if (aggregatedTxn.getParticipants() == null) {
			return;
		}

		// Find MGV participants and aggregate their amounts
		for (TransformedParticipant participant : aggregatedTxn.getParticipants()) {
			if (PaymentSystemEnum.MGV.getPaymentSystemKey().equals(participant.getPaymentSystem())) {

				long totalMgvAmount = mgvTransactions.stream()
					.flatMap(txn -> txn.getParticipants().stream())
					.filter(p -> PaymentSystemEnum.MGV.getPaymentSystemKey().equals(p.getPaymentSystem())
							&& participant.getEntityId().equals(p.getEntityId()))
					.mapToLong(p -> p.getAmount() != null ? p.getAmount() : 0L)
					.sum();

				participant.setAmount(totalMgvAmount);

				// Update remarks to indicate aggregation
				String originalRemarks = participant.getRemarks();
				participant.setRemarks(originalRemarks + " (Multiple vouchers aggregated)");

				break;
			}
		}
	}

	/**
	 * Groups MGV transactions by order ID for aggregation
	 */
	public static Map<String, List<TransformedTransactionHistoryDetail>> groupMgvTransactionsByOrderId(
			final List<TransformedTransactionHistoryDetail> transactions) {

		return transactions.stream()
			.filter(MgvUtility::isMgvTransaction)
			.filter(MgvUtility::isMgvRedemption) // Only aggregate redemptions, not
													// purchases
			.collect(Collectors.groupingBy(txn -> getOrderId(txn)));
	}

	/**
	 * Gets the order ID from transaction context or system ID
	 */
	private static String getOrderId(final TransformedTransactionHistoryDetail txn) {
		if (txn.getContextMap() != null && txn.getContextMap().containsKey("orderId")) {
			return txn.getContextMap().get("orderId");
		}
		return txn.getTxnId(); // Fallback to transaction ID
	}

	/**
	 * Gets the user participant from the transaction
	 */
	private static TransformedParticipant getUserParticipant(final TransformedTransactionHistoryDetail txn) {
		if (txn.getParticipants() == null) {
			return null;
		}

		for (TransformedParticipant participant : txn.getParticipants()) {
			if (StringUtils.isNotBlank(txn.getEntityId()) && txn.getEntityId().equals(participant.getEntityId())) {
				return participant;
			}
		}

		return null;
	}

	/**
	 * Checks if MGV transactions should be aggregated based on configuration
	 */
	public static boolean shouldAggregateMgvTransactions() {
		// This can be made configurable via properties
		return true;
	}

	/**
	 * Creates a list of voucher names from aggregated MGV transactions
	 */
	public static List<String> getVoucherNames(final List<TransformedTransactionHistoryDetail> mgvTransactions) {
		List<String> voucherNames = new ArrayList<>();

		for (TransformedTransactionHistoryDetail txn : mgvTransactions) {
			if (txn.getParticipants() != null) {
				for (TransformedParticipant participant : txn.getParticipants()) {
					if (PaymentSystemEnum.MGV.getPaymentSystemKey().equals(participant.getPaymentSystem())
							&& participant.getOtherData() != null
							&& StringUtils.isNotBlank(participant.getOtherData().getName())) {
						voucherNames.add(participant.getOtherData().getName());
					}
				}
			}
		}

		return voucherNames.stream().distinct().collect(Collectors.toList());
	}

}
