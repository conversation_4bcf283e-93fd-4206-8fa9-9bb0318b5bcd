package com.org.panaroma.ingester.cache;

import com.aerospike.client.Record;
import com.aerospike.client.policy.ClientPolicy;
import com.aerospike.client.policy.CommitLevel;
import com.fasterxml.jackson.core.type.TypeReference;
import com.org.panaroma.commons.aerospikeExtend.AerospikeClientExtend;
import com.org.panaroma.commons.dto.MerchantData;
import com.org.panaroma.commons.dto.UserDetails;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public abstract class IcacheClient<T> implements Serializable {

	// Created a customised Aerospike Cache client for custome serialisation and
	// deserialisation
	protected static AerospikeClientExtend aerospikeClient = null;

	protected String aeroSpikeUrl;

	protected String aerospikeNamespace;

	protected Integer aerospikePort;

	protected Integer writeDefaultCacheSocketTimeout;

	protected Integer writeDefaultCacheTotalTimeout;

	protected Integer writeDefaultSleepBetweenRetries;

	protected Integer writeDefaultCacheExpiryTime;

	protected Integer readDefaultSleepBetweenRetries;

	protected Integer readDefaultCacheTotalTimeout;

	protected Integer readDefaultCacheSocketTimeout;

	protected String cacheKeyPrefix;

	@Autowired
	public IcacheClient(@Value("${aerospike.host-name}") final String aeroSpikeUrl,
			@Value("${aerospike.namespace}") final String aerospikeNamespace,
			@Value("${aerospike.port}") final Integer aerospikePort,
			@Value("${aerospike.writePolicyDefault.socketTimeout}") final Integer writeDefaultCacheSocketTimeout,
			@Value("${aerospike.writePolicyDefault.totalTimeout}") final Integer writeDefaultCacheTotalTimeout,
			@Value("${aerospike.writePolicyDefault.sleepBetweenRetries}") final Integer writeDefaultSleepBetweenRetries,
			@Value("${aerospike.writePolicyDefault.expiration}") final Integer writeDefaultCacheExpiryTime,
			@Value("${aerospike.readPolicyDefault.sleepBetweenRetries}") final Integer readDefaultSleepBetweenRetries,
			@Value("${aerospike.readPolicyDefault.totalTimeout}") final Integer readDefaultCacheTotalTimeout,
			@Value("${aerospike.readPolicyDefault.socketTimeout}") final Integer readDefaultCacheSocketTimeout,
			@Value("${cache.key.prefix}") final String cacheKeyPrefix) {
		this.aeroSpikeUrl = aeroSpikeUrl;
		this.aerospikeNamespace = aerospikeNamespace;
		this.aerospikePort = aerospikePort;
		this.readDefaultCacheSocketTimeout = readDefaultCacheSocketTimeout;
		this.readDefaultCacheTotalTimeout = readDefaultCacheTotalTimeout;
		this.readDefaultSleepBetweenRetries = readDefaultSleepBetweenRetries;
		this.writeDefaultCacheExpiryTime = writeDefaultCacheExpiryTime;
		this.writeDefaultCacheSocketTimeout = writeDefaultCacheSocketTimeout;
		this.writeDefaultCacheTotalTimeout = writeDefaultCacheTotalTimeout;
		this.writeDefaultSleepBetweenRetries = writeDefaultSleepBetweenRetries;
		this.cacheKeyPrefix = cacheKeyPrefix;
		ensureInitialized();
	}

	/*
	 * Always assign aerospikeClient in last other wise it has double checked locking
	 * problem
	 */
	protected void ensureInitialized() {
		if (aerospikeClient != null) {
			return;
		}

		synchronized (this) {
			if (aerospikeClient == null) {
				log.debug("Initializing aerospike client.");
				ClientPolicy policy = new ClientPolicy();
				policy.writePolicyDefault.expiration = this.writeDefaultCacheExpiryTime;
				policy.writePolicyDefault.commitLevel = CommitLevel.COMMIT_ALL;
				policy.writePolicyDefault.socketTimeout = this.writeDefaultCacheSocketTimeout;
				policy.writePolicyDefault.totalTimeout = this.writeDefaultCacheTotalTimeout;
				policy.writePolicyDefault.sleepBetweenRetries = this.writeDefaultSleepBetweenRetries;
				policy.readPolicyDefault.socketTimeout = this.readDefaultCacheSocketTimeout;
				policy.readPolicyDefault.totalTimeout = this.readDefaultCacheTotalTimeout;
				policy.readPolicyDefault.sleepBetweenRetries = this.readDefaultSleepBetweenRetries;
				aerospikeClient = new AerospikeClientExtend(policy, aeroSpikeUrl, aerospikePort);
			}
		}
	}

	public abstract T getUpdatedRecord(String key);

	public abstract List<T> getUpdatedRecordList(String key);

	public abstract void saveUpdateRecordList(List<T> t, String key);

	public abstract void saveLocalisedRecordsToCache(Map<String, String> resultedMap, String langId);

	public abstract void saveUnlocalisedRecordsToCache(List<String> data, String langId);

	public abstract Set<String> getUnlocalisedRecordsfromCache(List<String> cacheBatchKey);

	public abstract Set<String> getAlreadyConsumedBatchRecordsFromCache(List<String> cacheBatchKey);

	public abstract void saveProcessedRecordsToCache(String langId, List<String> data);

	public abstract Map<String, String> getLocalisedBatchRecords(List<String> cacheBatchKey);

	public abstract void saveUpdatedRecord(T t, String key);

	public abstract void saveUpdatedCustIdsForOrder(List<String> custIds, String key);

	public abstract List<String> getUpdatedRecords(String key);

	public abstract MerchantData getUpdatedMerchantData(String key);

	public abstract void saveUpdateMerchantData(MerchantData merchantData, String key);

	public abstract void saveRecordInPpblCache(List<T> t, String key, String setName);

	public abstract List<T> getPpblRecord(String key);

	public abstract Record getRecord(String key, String setName, TypeReference type);

	public abstract void saveUpdateToken(String tokenCacheKey, String tokenValue);

	public abstract String getToken(String tokenCacheKey);

	public Map<String, UserDetails> getUserUrlfromCache(final List<String> key) {
		return null;
	}

	public void saveUserImageUrlRecordsToCache(final Map<String, UserDetails> userImageUrlData, final Integer ttl) {
		return;
	}

	public void saveAccRefNumInCache(final String accNo, final String ifsc, final String accRefNo) {
		return;
	}

	public String getAccRefNumFromCache(final String accNo, final String ifsc) {
		return null;
	}

	public Record getRecordFromCache(final String key, final String set, final TypeReference type) throws Exception {
		return null;
	}

	public void saveRecordInCache(final String key, final String set, final Integer expiryTime, final Object record)
			throws Exception {
		return;
	}

	public abstract void evict(final String key, final String set);

}
