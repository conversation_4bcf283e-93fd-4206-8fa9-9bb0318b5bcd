package com.org.panaroma.ingester.sources.configuration;

import static com.org.panaroma.commons.constants.CommonConstants.UPI_BACK_FILLING_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.CART;
import static com.org.panaroma.ingester.constants.Constants.CBS;
import static com.org.panaroma.ingester.constants.Constants.CBS_DATA_CONEVRTER_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.CHAT_BACK_FILLING_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.CHAT_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.MARKETPLACE;
import static com.org.panaroma.ingester.constants.Constants.PG_RETRY_V2_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.RECENT_TXN_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.RECON_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.RETRY_BACK_FILLING_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.RETRY_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.RETRY_PIPELINE_V2;
import static com.org.panaroma.ingester.constants.Constants.STATUS_RESOLVER;
import static com.org.panaroma.ingester.constants.Constants.TAGS_ENRICHER;
import static com.org.panaroma.ingester.constants.Constants.TOGGLE_VISIBILITY_PIPELINE;
import static com.org.panaroma.ingester.constants.Constants.USER_IMAGE_URL;
import static com.org.panaroma.ingester.constants.Constants.UTH_PIPELINE;
import static com.org.panaroma.ingester.constants.PipelineConstants.*;
import static com.org.panaroma.ingester.constants.PipelineConstants.MANDATE_RETRY;
import static org.apache.kafka.clients.consumer.ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG;
import static org.apache.kafka.clients.consumer.ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG;

import com.org.panaroma.commons.dto.BackFillingQueryObject;
import com.org.panaroma.commons.dto.ChatBackFillingKafkaObject;
import com.org.panaroma.commons.dto.KafkaUrlImageData;
import com.org.panaroma.commons.dto.MarketplaceKafkaRecord;
import com.org.panaroma.commons.dto.PostStitchData;
import com.org.panaroma.commons.dto.ReconKafkaObject;
import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.analytics.RecapCachePopulationDto;
import com.org.panaroma.commons.dto.analytics.UserIdFetcherDto;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.dto.spendDtos.kafka.UserMonthAggKafkaConfig;
import com.org.panaroma.commons.dto.spendDtos.kafka.UserSpendKafkaConfig;
import com.org.panaroma.commons.kafka.dto.CacheUpdaterKafkaDto;
import com.org.panaroma.ingester.configuration.kafka.CustomKafkaDeserializer;
import com.org.panaroma.ingester.configuration.kafka.GenericStringKafkaDeserializer;
import com.org.panaroma.ingester.configuration.kafka.GgKafkaDeserializer;
import com.org.panaroma.ingester.configuration.kafka.KafkaConfiguration;
import com.org.panaroma.ingester.configuration.kafka.MandateRetryKafkaDeserializer;
import com.org.panaroma.ingester.configuration.kafka.XmlToJsonDeserializer;
import com.org.panaroma.ingester.configuration.kafka.objects.KafkaSourceConfigurationObject;
import com.org.panaroma.ingester.cst.dto.GenericKafkaDto;
import com.org.panaroma.ingester.dto.*;
import com.org.panaroma.ingester.dto.insertUpdateExecutorDtos.GenericUpdateInsertDto;
import com.org.panaroma.ingester.monitoring.MetricsAgent;
import com.org.panaroma.ingester.sources.configuration.interfaces.ISourcesConfiguration;
import com.org.panaroma.ingester.sources.objects.FlinkSource;
import com.org.panaroma.ingester.transformer.CustomAvroDeserializationSchema;
import com.org.panaroma.ingester.transformer.MarketplaceKafkaDeserializer;
import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class KafkaFlinkSourcesConfiguration implements ISourcesConfiguration<FlinkSource> {

	private final KafkaConfiguration kafkaConfiguration;

	private final MetricsAgent metricsAgent;

	@Autowired
	public KafkaFlinkSourcesConfiguration(final KafkaConfiguration kafkaConfiguration,
			final MetricsAgent metricsAgent) {
		this.kafkaConfiguration = kafkaConfiguration;
		this.metricsAgent = metricsAgent;
	}

	@Override
	@Bean
	public List<FlinkSource> getSourceFunctions() {
		List<FlinkSource> sourceList = kafkaConfiguration.getSourceList()
			.stream()
			.map(this::createSourceFunction)
			.collect(Collectors.toList());

		return sourceList;
	}

	private FlinkSource createSourceFunction(final KafkaSourceConfigurationObject kafkaSourceConfigurationObject) {
		final Properties properties = new Properties();
		properties.setProperty("bootstrap.servers", kafkaSourceConfigurationObject.getBootstrapServersAsString());
		properties.setProperty("group.id", kafkaSourceConfigurationObject.getConsumerGroupName());
		properties.setProperty("max.poll.records", kafkaSourceConfigurationObject.getMaxPollRecords());
		if (kafkaSourceConfigurationObject.getPollInterval() != null) {
			properties.setProperty(MAX_POLL_INTERVAL_MS_CONFIG, kafkaSourceConfigurationObject.getPollInterval());
		}
		if (kafkaSourceConfigurationObject.getSessionTimeout() != null) {
			properties.setProperty(SESSION_TIMEOUT_MS_CONFIG, kafkaSourceConfigurationObject.getSessionTimeout());
		}
		FlinkKafkaConsumer kafkaConsumer = null;
		switch (kafkaSourceConfigurationObject.getPipelineName()) {
			case RETRY_PIPELINE:
			case PG_RETRY_V2_PIPELINE:
			case CART:
			case STATUS_RESOLVER:
			case UTH_PIPELINE:
			case RETRY_PIPELINE_V2:
			case RETRY_BACK_FILLING_PIPELINE:
			case DC_MAIN_PIPELINE:
			case DC_UTH_ENRICHER_PIPELINE:
			case DATA_AUDIT_PIPELINE:
			case CHAT_DATA_PUBLISH_API_PIPELINE:
			case TOGGLE_VISIBILITY_PIPELINE:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new CustomKafkaDeserializer(true), properties);
				break;
			case MARKETPLACE:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new MarketplaceKafkaDeserializer(kafkaSourceConfigurationObject.getTopic(),
								kafkaSourceConfigurationObject.getConfluentKafkaRegistryUrl(),
								MarketplaceKafkaRecord.class),
						properties);
				break;
			case CBS_DATA_CONEVRTER_PIPELINE:
			case CBS:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new GgKafkaDeserializer(true), properties);
				break;
			case CHAT_PIPELINE:
			case RECENT_TXN_PIPELINE:
			case PROMO_UPI_PIPELINE:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new CustomAvroDeserializationSchema(kafkaSourceConfigurationObject.getTopic(),
								kafkaSourceConfigurationObject.getConfluentKafkaRegistryUrl(),
								TransformedTransactionHistoryDetail.class),
						properties);
				break;
			case USER_IMAGE_URL:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new GenericStringKafkaDeserializer(true, KafkaUrlImageData.class), properties);
				break;
			case CHAT_BACK_FILLING_PIPELINE:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new GenericStringKafkaDeserializer(true, ChatBackFillingKafkaObject.class), properties);
				break;
			case RECON_PIPELINE:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new GenericStringKafkaDeserializer(true, ReconKafkaObject.class), properties);
				break;
			case UPI_BACK_FILLING_PIPELINE:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new GenericStringKafkaDeserializer(true, BackFillingQueryObject.class), properties);
				break;
			case CBS_STREAM_PIPELINE:
			case CBS_RETRY_PIPELINE:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new GenericStringKafkaDeserializer(true, PostStitchData.class), properties);
				break;
			case PROMO_PIPELINE:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new GenericStringKafkaDeserializer(true, TransformedTransactionHistoryDetail.class),
						properties);
				break;
			case UPI_RECON_PIPELINE:
			case VAN_PIPELINE:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new XmlToJsonDeserializer(true, XmlToJsonDto.class), properties);
				break;
			case CACHE_UPDATER_PIPELINE:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new GenericStringKafkaDeserializer(true, CacheUpdaterKafkaDto.class), properties);
				break;
			case USER_SPEND_DOCS_CREATOR_PIPELINE:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new GenericStringKafkaDeserializer(true, UserSpendKafkaConfig.class), properties);
				break;
			case USER_ANALYTICS_MONTH_AGG_CREATOR:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new GenericStringKafkaDeserializer(true, UserMonthAggKafkaConfig.class), properties);
				break;
			case USERID_FETHCER_PIPELINE:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new GenericStringKafkaDeserializer(true, UserIdFetcherDto.class), properties);
				break;

			case TAGS_ENRICHER:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new GenericStringKafkaDeserializer(true, TxnTagsData.class), properties);
				break;
			case GENERIC_RETRY_PIPELINE:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new GenericStringKafkaDeserializer(true, BaseRetryDto.class), properties);

				break;
			case UDIR:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new GenericStringKafkaDeserializer(true, GenericKafkaDto.class), properties);
				break;
			case ES_INSERT_UPDATE_EXECUTOR_PIPELINE:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new GenericStringKafkaDeserializer(true, GenericUpdateInsertDto.class), properties);
				break;
			case API_RESPONSE_CACHE_POPULATION_PIPELINE:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new GenericStringKafkaDeserializer(true, RecapCachePopulationDto.class), properties);
				break;

			case OMS:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new GenericStringKafkaDeserializer(true, OmsDto.class), properties);
				break;

			case MANDATE_RETRY:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new MandateRetryKafkaDeserializer(), properties);
				break;

			case OMS_REFUND:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new GenericStringKafkaDeserializer(true, OmsRefundDto.class), properties);
				break;

			default:
				kafkaConsumer = new FlinkKafkaConsumer(kafkaSourceConfigurationObject.getTopic(),
						new CustomAvroDeserializationSchema(kafkaSourceConfigurationObject.getTopic(),
								kafkaSourceConfigurationObject.getConfluentKafkaRegistryUrl(),
								TransactionHistoryDetails.class),
						properties);
				// kafkaConsumer.setStartFromLatest();
				break;
		}

		return new FlinkSource(kafkaSourceConfigurationObject.getPipelineName(), kafkaConsumer);
	}

}
