package com.org.panaroma.ingester.merger;

import static com.org.panaroma.commons.constants.CommonConstants.IGNORED_PARTICIPANT;
import static com.org.panaroma.commons.constants.CommonConstants.IS_ADDED_PARTICIPANT_BY_MERGING;
import static com.org.panaroma.commons.constants.CommonConstants.IS_WALLET_INETROP_TXN;
import static com.org.panaroma.commons.constants.WebConstants.UMN;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2M;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.P2M_REFUND;
import static com.org.panaroma.commons.dto.TransactionTypeEnum.RECURRING_MANDATE;
import static com.org.panaroma.ingester.constants.Constants.CONTEXT_MAP;
import static com.org.panaroma.ingester.constants.Constants.CREDIT;
import static com.org.panaroma.ingester.constants.Constants.CREDIT_PAYMENT_SYSTEM;
import static com.org.panaroma.ingester.constants.Constants.DEBIT;
import static com.org.panaroma.ingester.constants.Constants.DEBIT_PAYMENT_SYSTEM;
import static com.org.panaroma.ingester.constants.Constants.DOC_UPDATED_DATE;
import static com.org.panaroma.ingester.constants.Constants.IS_CHAT_DOCUMENT;
import static com.org.panaroma.ingester.constants.Constants.IS_MERGED_DOCUMENT;
import static com.org.panaroma.ingester.constants.Constants.LOCATION;
import static com.org.panaroma.ingester.constants.Constants.P2M_STREAM_SOURCES_TO_SEND_TO_CHAT;
import static com.org.panaroma.ingester.constants.Constants.PARENT_DETAILS;
import static com.org.panaroma.ingester.constants.Constants.REFUND_DETAILS;
import static com.org.panaroma.ingester.constants.Constants.SEARCH_FIELDS;
import static com.org.panaroma.ingester.constants.Constants.SPEND_ANALYTICS_FIELDS;
import static com.org.panaroma.ingester.constants.Constants.TRUE;
import static com.org.panaroma.ingester.constants.Constants.WALLET_PARTICIPANT_LIST;
import static com.org.panaroma.ingester.constants.Constants.WALLET_PAYMENT_SYSTEM;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.TransactionIndicator;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.es.TransformedParticipant;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.datagroup.GroupingManager;
import com.org.panaroma.ingester.repository.EsRepository;
import com.org.panaroma.ingester.utils.MergerUtility;
import com.org.panaroma.ingester.utils.Utility;
import com.org.panaroma.commons.utils.MgvUtility;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class P2mMerger implements UthMerger {

	@Autowired
	EsRepository esRepository;

	@Autowired
	ObjectMapper objectMapper;

	@Autowired
	AddAndPayRefundMerger addAndPayRefundMerger;

	@Autowired
	AddMoneyRefundMerger addMoneyRefundMerger;

	@Autowired
	WalletInterOpMerger walletInterOpMerger;

	@Autowired
	GroupingManager groupingManager;

	@Value("${white.listed.users.list}")
	private List<String> whiteListedUserList;

	@Value("${p2mAndAddAndPay.whitelisting.required.for.chat}")
	private Integer p2mAndAddAndPayWhitelistingReqForChat;

	@Value("${p2mAndAddAndPay.rollOut.percent.for.chat}")
	private Double p2mAndAddAndPayRollOutPercentageForChat;

	@Autowired
	private AddAndPayMerger addAndPayMerger;

	@Override
	public List<TransactionTypeEnum> appliesTo() {
		return Arrays.asList();
	}

	private static final String DEBIT_UPI_DATA = "debitUpiData";

	private static final String DEBIT_WALLET_DATA = "debitWalletData";

	private static final String DEBIT_BANK_DATA = "debitBankData";

	private static final String DEBIT_USER_CARD_DATA = "debitUserCardData";

	private static final String DEBIT_NAME = "debitName";

	private static final String DEBIT_CONTEXT_MAP = "debitContextMap";

	private static final String CREDIT_UPI_DATA = "creditUpiData";

	private static final String CREDIT_WALLET_DATA = "creditWalletData";

	private static final String CREDIT_NAME = "creditName";

	private static final String CREDIT_BANK_DATA = "creditBankData";

	private static final String CREDIT_USER_CARD_DATA = "creditUserCardData";

	private static final String CREDIT_CONTEXT_MAP = "creditContextMap";

	private static final String MERCHANT_NAME = "merchantName";

	private static final String MERCHANT_UPI_DATA = "merchantUpiData";

	private static final String MERCHANT_BANK_DATA = "merchantBankData";

	private static final String MERCHANT_ENTITY_TYPE = "merchantEntityType";

	private static final String MERCHANT_CONTEXT_MAP = "merchantContextMap";

	private Set<TransformedTransactionHistoryDetail> getAllRelativeDocs(
			final Set<TransformedTransactionHistoryDetail> enrichedDocs) {
		Optional<TransformedTransactionHistoryDetail> pgDocOptional = enrichedDocs.stream()
			.filter((doc) -> TransactionSource
				.isPgTypeSource(TransactionSource.getTransactionSourceEnumByKey(doc.getStreamSource())))
			.findFirst();

		// For wallet InterOp case where UPi and wallet ar just involved UPI-->WALLET
		if (!pgDocOptional.isPresent()) {
			return enrichedDocs;
		}

		Set<TransformedTransactionHistoryDetail> relativeDocs = new HashSet<>();

		TransformedTransactionHistoryDetail pgDoc = pgDocOptional.get();

		boolean isUpiExistForTxn = pgDoc.getParticipants()
			.stream()
			.anyMatch(
					(participant -> (PaymentSystemEnum.UPI.getPaymentSystemKey().equals(participant.getPaymentSystem())
							&& pgDoc.getEntityId().equals(participant.getEntityId()))));

		boolean isWalletExistForTxn = pgDoc.getParticipants()
			.stream()
			.anyMatch((participant -> (PaymentSystemEnum.WALLET.getPaymentSystemKey()
				.equals(participant.getPaymentSystem()) && pgDoc.getEntityId().equals(participant.getEntityId()))));

		boolean isWalletUpiHybridTxn = isUpiExistForTxn && isWalletExistForTxn;

		// if this is not wallet UPI hybrid txn then return same grouped dtos
		if (Boolean.FALSE.equals(isWalletUpiHybridTxn)) {
			return enrichedDocs;
		}

		boolean isWalletDocPresent = enrichedDocs.stream()
			.anyMatch((doc) -> TransactionSource.WALLET.getTransactionSourceKey().equals(doc.getStreamSource()));
		boolean isUpiDocPresent = enrichedDocs.stream()
			.anyMatch((doc) -> TransactionSource.UPI.getTransactionSourceKey().equals(doc.getStreamSource()));

		// if both UPI and wallet doc exist in current relative Doc set then return
		// current Doc set
		if (isWalletDocPresent && isUpiDocPresent) {
			return enrichedDocs;
		}

		relativeDocs = groupingManager.group(pgDoc).getValue();
		relativeDocs.add(pgDoc);
		return relativeDocs;
	}

	@Override
	public Set<TransformedTransactionHistoryDetail> merge(
			final Set<TransformedTransactionHistoryDetail> passedEnrichedDocs) {
		Set<TransformedTransactionHistoryDetail> enrichedDocs = passedEnrichedDocs;

		if (Objects.isNull(enrichedDocs) || enrichedDocs.size() <= 1) {
			return enrichedDocs;
		}

		// getting all relative Docs if missing in current set
		enrichedDocs = getAllRelativeDocs(enrichedDocs);

		Optional<TransformedTransactionHistoryDetail> pgDoc = enrichedDocs.stream()
			.filter((doc) -> TransactionSource
				.isPgTypeSource(TransactionSource.getTransactionSourceEnumByKey(doc.getStreamSource())))
			.findFirst();

		TransformedTransactionHistoryDetail pgDocument = null;
		if (pgDoc.isPresent()) {
			pgDocument = pgDoc.get();
		}

		if (com.org.panaroma.commons.utils.Utility.isAddnPayRefundTxn(pgDocument)) {
			return addAndPayRefundMerger.merge(enrichedDocs);
		}

		Optional<TransformedTransactionHistoryDetail> upiDoc = enrichedDocs.stream()
			.filter((doc) -> TransactionSource.UPI
				.equals(TransactionSource.getTransactionSourceEnumByKey(doc.getStreamSource())))
			.findFirst();

		TransformedTransactionHistoryDetail upiDocument = null;
		if (upiDoc.isPresent()) {
			upiDocument = upiDoc.get();
		}

		Optional<TransformedTransactionHistoryDetail> walletDoc = enrichedDocs.stream()
			.filter((doc) -> TransactionSource.WALLET
				.equals(TransactionSource.getTransactionSourceEnumByKey(doc.getStreamSource())))
			.findFirst();

		TransformedTransactionHistoryDetail walletDocument = null;
		if (walletDoc.isPresent()) {
			walletDocument = walletDoc.get();
		}

		// Diverting to wallet InterOp merger for WalletInterOp Txns
		if (Utility.isWalletInterOpTxn(walletDocument) || Utility.isWalletInterOpTxn(upiDocument)) {
			return walletInterOpMerger.merge(enrichedDocs);
		}

		// Not merging if PG showInListing is not true
		if (!needDocsMerging(enrichedDocs)) {
			return enrichedDocs;
		}

		populateLocationInfo(enrichedDocs, pgDocument);

		if (!upiDoc.isPresent() && !walletDoc.isPresent()) {
			Set<TransformedTransactionHistoryDetail> mergeDocSet = new HashSet<>();
			mergeDocSet.add(pgDocument);
			return mergeDocSet;
		}

		// condition - (pg doc is null or invalid and (wallet doc or upi doc both are null
		// or invalid))
		if ((Objects.isNull(pgDocument.getParticipants()) || pgDocument.getParticipants().size() == 0) &&
		// upi and wallet doc are valid
				((Objects.isNull(upiDocument) || Objects.isNull(upiDocument.getParticipants())
						|| upiDocument.getParticipants().size() == 0)
						&& (Objects.isNull(walletDocument) || Objects.isNull(walletDocument.getParticipants())
								|| walletDocument.getParticipants().size() == 0))) {
			return enrichedDocs;
		}

		MergerUtility.addUpiDetailsToPgDoc(pgDocument, upiDocument);

		// in case of retry or backfill, remove all the participants from pgDoc that were
		// merged from walletDoc
		// so that we can merge wallet events again
		if (Objects.nonNull(walletDocument) && ObjectUtils.isNotEmpty(walletDocument.getParticipants())) {
			removeAddedParticipantByMerging(pgDocument);
		}

		MergerUtility.addWalletDetailsToPgDoc(pgDocument, walletDocument);

		Set<TransformedTransactionHistoryDetail> mergeDocSet = new HashSet<>();
		if (TransactionSource.isPgTypeSource(pgDocument.getStreamSource())
				&& TransactionTypeEnum.ADD_AND_PAY.getTransactionTypeKey().equals(pgDocument.getTxnType())) {
			Set<TransformedTransactionHistoryDetail> transformedTransactionHistoryDetailSet = addAndPayMerger
				.merge(enrichedDocs);
			if (!transformedTransactionHistoryDetailSet.isEmpty()) {
				mergeDocSet.addAll(transformedTransactionHistoryDetailSet);
			}
			else {
				mergeDocSet.add(pgDocument);
			}
		}
		else {
			mergeDocSet.add(pgDocument);
			setMergeFlag(pgDocument);
		}
		return mergeDocSet;
	}

	private void removeAddedParticipantByMerging(final TransformedTransactionHistoryDetail tthd) {
		if (Objects.nonNull(tthd) && ObjectUtils.isNotEmpty(tthd.getParticipants())) {
			tthd.getParticipants()
				.removeIf(participant -> (participant.getContextMap() != null
						&& participant.getContextMap().containsKey(IS_ADDED_PARTICIPANT_BY_MERGING)));
		}
	}

	@Override
	public UpdateRequest createUpdateRequest(final TransformedTransactionHistoryDetail tthd)
			throws JsonProcessingException {
		if (tthd.getContextMap() != null && TRUE.equalsIgnoreCase(tthd.getContextMap().get(IS_WALLET_INETROP_TXN))) {
			UpdateRequest updateRequest = walletInterOpMerger.createUpdateRequest(tthd);
			return updateRequest;
		}

		if (!TransactionSource.isPgTypeSource(tthd.getStreamSource())) {
			return null;
		}

		if (tthd.getContextMap() == null || !tthd.getContextMap().containsKey(IS_MERGED_DOCUMENT)) {
			if (CollectionUtils.isEmpty(tthd.getRefundDetails()) && Objects.isNull(tthd.getParentDetails())) {
				return null;
			}
		}

		if (com.org.panaroma.commons.utils.Utility.isAddnPayRefundTxn(tthd)) {
			UpdateRequest updateRequest = addAndPayRefundMerger.createUpdateRequest(tthd);
			updateRequest.index(esRepository.getIndexName(tthd));
			return updateRequest;
		}
		if (tthd.getTxnType().equals(TransactionTypeEnum.ADD_MONEY_REFUND.getTransactionTypeKey())) {
			UpdateRequest updateRequest = addMoneyRefundMerger.createUpdateRequest(tthd);
			updateRequest.index(esRepository.getIndexName(tthd));
			return updateRequest;

		}
		UpdateRequest updateRequest = new UpdateRequest();
		updateRequest.index(esRepository.getIndexName(tthd));
		updateRequest.type("_doc");
		updateRequest.id(tthd.docId());
		updateRequest.routing(tthd.getEntityId());
		Map<String, Object> parameters = getParameters(tthd);
		String scriptString = getScript(parameters);

		Script script = new Script(ScriptType.INLINE, "painless", scriptString, parameters);
		log.info("script : {} for docId : {}", script, tthd.docId());
		updateRequest.script(script);

		return updateRequest;
	}

	@Override
	public void populateChatFlagInDtosToSendToChat(final Set<TransformedTransactionHistoryDetail> groupedDtos,
			final Set<TransformedTransactionHistoryDetail> mergedDtos,
			final TransformedTransactionHistoryDetail currentDoc) {
		final Boolean isWhitelistedTxnForChat = com.org.panaroma.commons.utils.Utility
			.isUserWhitelistedFromPercentageOrUserList(p2mAndAddAndPayRollOutPercentageForChat,
					p2mAndAddAndPayWhitelistingReqForChat, whiteListedUserList, currentDoc);
		if (isWhitelistedTxnForChat) {
			TransactionSource streamSource = TransactionSource
				.getTransactionSourceEnumByKey(currentDoc.getStreamSource());
			if (groupedDtos.size() == 1 && P2M_STREAM_SOURCES_TO_SEND_TO_CHAT.contains(streamSource)
					&& !Utility.isUpiP2mRefund(currentDoc)
					&& !com.org.panaroma.commons.utils.Utility.isMandateTransaction(currentDoc)
					&& Boolean.TRUE.equals(currentDoc.getShowInListing())
					&& Boolean.TRUE.equals(currentDoc.getIsVisible())) {
				Utility.populateIsChatDocFlagInContextMap(currentDoc);
				mergedDtos.add(currentDoc);
			}
			else if (Objects.nonNull(mergedDtos) && mergedDtos.size() == 1) {
				TransformedTransactionHistoryDetail mergedDto = mergedDtos.iterator().next();
				if (Boolean.TRUE.equals(mergedDto.getShowInListing())
						&& Boolean.TRUE.equals(mergedDto.getIsVisible())) {
					Utility.populateIsChatDocFlagInContextMap(mergedDto);
				}
			}
			else if (Utility.isUpiP2mCollectTxn(currentDoc)) {
				Utility.populateIsChatDocFlagInContextMap(currentDoc);
				mergedDtos.add(currentDoc);
			}
		}
		else {
			// this else block is only for QA testing & can be removed afterwards
			log.info(
					"Not attempting to populate isChatDocument flag as entityId : {} in event received corresponding to txnId : {} is"
							+ "not whitelisted",
					currentDoc.getEntityId(), currentDoc.getTxnId());
		}
	}

	private String getScript(final Map<String, Object> parameters) {
		String scriptString = "ctx._source.docUpdatedDate = params.docUpdatedDate;";

		if (Objects.isNull(parameters.get(CONTEXT_MAP))) {
			scriptString = scriptString
					+ "if (ctx._source.contextMap == null) { ctx._source.contextMap = ['isMergedDocument' : params.isMergedDocument];} "
					+ "else {ctx._source.contextMap.isMergedDocument = params.isMergedDocument; }";
		}
		else {
			scriptString = scriptString + "ctx._source.contextMap = params.contextMap;";
		}

		scriptString = scriptString + "for(int i=0;i<ctx._source['participants'].size();i++) {";

		String script1 = "";

		if (parameters.get(DEBIT_NAME) != null) {
			script1 = script1 + "ctx._source.participants[i].name = params.debitName; ";
		}
		if (parameters.get(DEBIT_BANK_DATA) != null) {
			script1 = script1 + "ctx._source.participants[i].bankData = params.debitBankData; ";
		}
		if (parameters.get(DEBIT_UPI_DATA) != null) {
			script1 = script1 + "ctx._source.participants[i].upiData = params.debitUpiData; ";
		}
		if (parameters.get(DEBIT_USER_CARD_DATA) != null) {
			script1 = script1 + "ctx._source.participants[i].cardData = params.debitUserCardData; ";
		}
		if (parameters.get(DEBIT_CONTEXT_MAP) != null) {
			script1 = script1 + "ctx._source.participants[i].contextMap = params.debitContextMap; ";
		}

		if (StringUtils.isNotBlank(script1)) {
			scriptString = scriptString + "if(ctx._source.participants[i].txnIndicator == params.debit "
					+ "&& ctx._source.participants[i].paymentSystem == params.debitPaymentSystem) {" + script1 + "}";
		}

		String script2 = "";

		if (parameters.get(CREDIT_NAME) != null) {
			script2 = script2 + " ctx._source.participants[i].name = params.creditName; ";
		}
		if (parameters.get(CREDIT_BANK_DATA) != null) {
			script2 = script2 + " ctx._source.participants[i].bankData = params.creditBankData; ";
		}
		if (parameters.get(CREDIT_UPI_DATA) != null) {
			script2 = script2 + "ctx._source.participants[i].upiData = params.creditUpiData; ";
		}
		if (parameters.get(CREDIT_USER_CARD_DATA) != null) {
			script2 = script2 + "ctx._source.participants[i].cardData = params.creditUserCardData; ";
		}
		if (parameters.get(CREDIT_CONTEXT_MAP) != null) {
			script2 = script2 + "ctx._source.participants[i].contextMap = params.creditContextMap; ";
		}

		if (StringUtils.isNotBlank(script2)) {
			if (StringUtils.isNotBlank(script1)) {
				scriptString = scriptString + "else ";
			}
			scriptString = scriptString + "if (ctx._source.participants[i].txnIndicator == params.credit"
					+ "&& ctx._source.participants[i].paymentSystem == params.creditPaymentSystem) {" + script2 + "}";
		}

		String script3 = "";

		if (parameters.get(MERCHANT_NAME) != null) {
			script3 = script3 + " ctx._source.participants[i].name = params.merchantName; ";
		}
		if (parameters.get(MERCHANT_BANK_DATA) != null) {
			script3 = script3 + " ctx._source.participants[i].bankData = params.merchantBankData; ";
		}
		if (parameters.get(MERCHANT_UPI_DATA) != null) {
			script3 = script3 + "ctx._source.participants[i].upiData = params.merchantUpiData; ";
		}
		if (parameters.get(MERCHANT_CONTEXT_MAP) != null) {
			script3 = script3 + "ctx._source.participants[i].contextMap = params.merchantContextMap; ";
		}

		if (StringUtils.isNotBlank(script3)) {
			scriptString = scriptString + "if (ctx._source.participants[i].entityType == params.merchantEntityType) {"
					+ script3 + "}";
		}

		scriptString = scriptString + "}";

		if (ObjectUtils.isNotEmpty(parameters.get(WALLET_PARTICIPANT_LIST))) {
			scriptString = scriptString
					+ " ctx._source.participants.removeIf(participant-> participant.paymentSystem == params.walletPaymentSystem);"
					+ " ctx._source.participants.addAll(params.walletParticipantList); ";
		}
		if (parameters.containsKey(SEARCH_FIELDS) && ObjectUtils.isNotEmpty(parameters.get(SEARCH_FIELDS))) {
			scriptString += " ctx._source.searchFields = params.searchFields; ";
		}
		if (parameters.containsKey(LOCATION) && ObjectUtils.isNotEmpty(parameters.get(LOCATION))) {
			scriptString += " ctx._source.location = params.location; ";
		}

		if (parameters.containsKey(SPEND_ANALYTICS_FIELDS)
				&& ObjectUtils.isNotEmpty(parameters.get(SPEND_ANALYTICS_FIELDS))) {
			scriptString += " ctx._source.spendAnalyticsFields = params.spendAnalyticsFields; ";
		}

		if (parameters.containsKey(REFUND_DETAILS) && Objects.nonNull(parameters.get(REFUND_DETAILS))) {
			scriptString += " ctx._source.refundDetails = params.refundDetails; ";
		}

		if (parameters.containsKey(PARENT_DETAILS) && Objects.nonNull(parameters.get(PARENT_DETAILS))) {
			scriptString += " ctx._source.parentDetails = params.parentDetails; ";
		}

		if (parameters.containsKey(UMN) && Objects.nonNull(parameters.get(UMN))) {
			scriptString += " ctx._source.umn = params.umn; ";
		}

		return scriptString;
	}

	private Map<String, Object> getParameters(final TransformedTransactionHistoryDetail tthd)
			throws JsonProcessingException {
		Map<String, Object> parameters = new HashMap<>();
		String isMergeDocument = tthd.getContextMap() == null ? null
				: tthd.getContextMap().getOrDefault(IS_MERGED_DOCUMENT, null);
		parameters.put(IS_MERGED_DOCUMENT, isMergeDocument);
		List<TransformedParticipant> walletTransformedParticipantList = new ArrayList<>();
		for (TransformedParticipant participant : tthd.getParticipants()) {
			if (TransactionIndicator.DEBIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
				if (PaymentSystemEnum.UPI.getPaymentSystemKey().equals(participant.getPaymentSystem())) {
					String upiDataString = objectMapper.writeValueAsString(participant.getUpiData());
					parameters.put(DEBIT_UPI_DATA, objectMapper.readValue(upiDataString, HashMap.class));
					String bankDataString = objectMapper.writeValueAsString(participant.getBankData());
					parameters.put(DEBIT_BANK_DATA, objectMapper.readValue(bankDataString, HashMap.class));
					// Upi via credit card handling
					String cardDataString = objectMapper.writeValueAsString(participant.getCardData());
					parameters.put(DEBIT_USER_CARD_DATA, objectMapper.readValue(cardDataString, HashMap.class));
					parameters.put(DEBIT_PAYMENT_SYSTEM, participant.getPaymentSystem());
					parameters.put(DEBIT_NAME, participant.getName());
				}
				if (PaymentSystemEnum.WALLET.getPaymentSystemKey().equals(participant.getPaymentSystem())
						&& isParticipantIgnoredOrMerged(participant)) {
					parameters.put(WALLET_PAYMENT_SYSTEM, PaymentSystemEnum.WALLET.getPaymentSystemKey());
					walletTransformedParticipantList.add(participant);
				}
				// Upi lite handling
				if (Utility.isUpiLiteTxnAndPaymentInstrument(participant)) {
					parameters.put(DEBIT_CONTEXT_MAP, participant.getContextMap());
				}

				parameters.put(DEBIT, participant.getTxnIndicator());
			}
			else if (TransactionIndicator.CREDIT.getTransactionIndicatorKey().equals(participant.getTxnIndicator())) {
				if (PaymentSystemEnum.UPI.getPaymentSystemKey().equals(participant.getPaymentSystem())) {
					String upiDataString = objectMapper.writeValueAsString(participant.getUpiData());
					parameters.put(CREDIT_UPI_DATA, objectMapper.readValue(upiDataString, HashMap.class));
					String bankDataString = objectMapper.writeValueAsString(participant.getBankData());
					parameters.put(CREDIT_BANK_DATA, objectMapper.readValue(bankDataString, HashMap.class));
					// Upi via credit card handling
					String cardDataString = objectMapper.writeValueAsString(participant.getCardData());
					parameters.put(CREDIT_USER_CARD_DATA, objectMapper.readValue(cardDataString, HashMap.class));
					parameters.put(CREDIT_PAYMENT_SYSTEM, participant.getPaymentSystem());
					parameters.put(CREDIT_NAME, participant.getName());
				}
				if (PaymentSystemEnum.WALLET.getPaymentSystemKey().equals(participant.getPaymentSystem())
						&& isParticipantIgnoredOrMerged(participant)) {
					parameters.put(WALLET_PAYMENT_SYSTEM, PaymentSystemEnum.WALLET.getPaymentSystemKey());
					walletTransformedParticipantList.add(participant);
				}

				// Upi lite handling
				if (Utility.isUpiLiteTxnAndPaymentInstrument(participant)) {
					parameters.put(CREDIT_CONTEXT_MAP, participant.getContextMap());
				}
				parameters.put(CREDIT, participant.getTxnIndicator());
			}

			if (EntityTypesEnum.MERCHANT.getEntityTypeKey().equals(participant.getEntityType())) {
				parameters.put(MERCHANT_NAME, participant.getName());

				String contextMapString = objectMapper.writeValueAsString(participant.getContextMap());
				parameters.put(MERCHANT_CONTEXT_MAP, objectMapper.readValue(contextMapString, HashMap.class));

				String upiDataString = objectMapper.writeValueAsString(participant.getUpiData());
				parameters.put(MERCHANT_UPI_DATA, objectMapper.readValue(upiDataString, HashMap.class));

				String bankDataString = objectMapper.writeValueAsString(participant.getBankData());
				parameters.put(MERCHANT_BANK_DATA, objectMapper.readValue(bankDataString, HashMap.class));

				parameters.put(MERCHANT_ENTITY_TYPE, EntityTypesEnum.MERCHANT.getEntityTypeKey());
			}
		}
		if (walletTransformedParticipantList.size() > 0) {
			String walletParticipantString = objectMapper.writeValueAsString(walletTransformedParticipantList);
			parameters.put(WALLET_PARTICIPANT_LIST, objectMapper.readValue(walletParticipantString, List.class));
		}
		parameters.put(DOC_UPDATED_DATE, tthd.getDocUpdatedDate());
		if (tthd.getSearchFields() != null) {

			String searchFieldsJson = objectMapper.writeValueAsString(tthd.getSearchFields());
			parameters.put(SEARCH_FIELDS, objectMapper.readValue(searchFieldsJson, HashMap.class));
		}

		if (tthd.getSpendAnalyticsFields() != null) {
			String spendFieldsJson = objectMapper.writeValueAsString(tthd.getSpendAnalyticsFields());
			parameters.put(SPEND_ANALYTICS_FIELDS, objectMapper.readValue(spendFieldsJson, HashMap.class));
		}

		if (CollectionUtils.isNotEmpty(tthd.getRefundDetails())) {
			String refundDetailsJson = objectMapper.writeValueAsString(tthd.getRefundDetails());
			parameters.put(REFUND_DETAILS, objectMapper.readValue(refundDetailsJson, List.class));
		}

		if (Objects.nonNull(tthd.getParentDetails())) {
			String parentDetailsJson = objectMapper.writeValueAsString(tthd.getParentDetails());
			parameters.put(PARENT_DETAILS, objectMapper.readValue(parentDetailsJson, HashMap.class));
		}

		if (Utility.isUpiLiteTxn(tthd) || Utility.isUpiViaCcTxn(tthd) || Utility.isRecurringMandateTxn(tthd)) {
			Map<String, String> contextMap = new HashMap<>(tthd.getContextMap());
			contextMap.remove(IS_CHAT_DOCUMENT);

			parameters.put(CONTEXT_MAP, contextMap);
		}
		if (Utility.isValidLocation(tthd)) {
			String location = objectMapper.writeValueAsString(tthd.getLocation());
			parameters.put(LOCATION, objectMapper.readValue(location, HashMap.class));
		}

		if (StringUtils.isNotBlank(tthd.getUmn())) {
			parameters.put(UMN, tthd.getUmn());
		}

		return parameters;
	}

	private boolean isParticipantIgnoredOrMerged(final TransformedParticipant participant) {
		if (Objects.isNull(participant) || ObjectUtils.isEmpty(participant.getContextMap())) {
			return false;
		}

		// if participant is merged
		if (TRUE.equalsIgnoreCase(participant.getContextMap().get(IS_ADDED_PARTICIPANT_BY_MERGING))) {
			return true;
		}

		// if participant is ignored
		if (TRUE.equalsIgnoreCase(participant.getContextMap().get(IGNORED_PARTICIPANT))) {
			return true;
		}

		return false;
	}

	private boolean needDocsMerging(final Set<TransformedTransactionHistoryDetail> enrichedDocs) {
		for (TransformedTransactionHistoryDetail thd : enrichedDocs) {
			if (TransactionSource.isPgTypeSource(TransactionSource.getTransactionSourceEnumByKey(thd.getStreamSource()))
					&& thd.getIsSource() && Boolean.TRUE.equals(thd.getShowInListing())) {
				return true;
			}
		}
		return false;
	}

	private void populateLocationInfo(final Set<TransformedTransactionHistoryDetail> enrichedDocs,
			final TransformedTransactionHistoryDetail pgDoc) {
		if (!Utility.isValidLocation(pgDoc)) {
			enrichedDocs.stream()
				.filter(Utility::isValidLocation)
				.findFirst()
				.ifPresent(tthd -> pgDoc.setLocation(tthd.getLocation()));
		}
	}

}