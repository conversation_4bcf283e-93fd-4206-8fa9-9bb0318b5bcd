package com.org.panaroma.ingester.merger;

import static com.org.panaroma.ingester.constants.Constants.IS_MERGED_DOCUMENT;
import static com.org.panaroma.ingester.constants.Constants.TRUE;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.panaroma.commons.dto.TransactionTypeEnum;
import com.org.panaroma.commons.dto.es.TransformedTransactionHistoryDetail;
import com.org.panaroma.commons.utils.MgvUtility;
import com.org.panaroma.ingester.repository.EsRepository;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.extern.log4j.Log4j2;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Merger for handling MGV transaction aggregation Aggregates multiple MGV redemptions in
 * a single transaction/order
 */
@Component
@Log4j2
public class MgvAggregationMerger implements BaseMerger {

	@Autowired
	EsRepository esRepository;

	@Autowired
	ObjectMapper objectMapper;

	@Override
	public List<TransactionTypeEnum> appliesTo() {
		return Arrays.asList(TransactionTypeEnum.P2M);
	}

	@Override
	public Set<TransformedTransactionHistoryDetail> merge(final Set<TransformedTransactionHistoryDetail> enrichedDocs) {
		if (enrichedDocs == null || enrichedDocs.isEmpty()) {
			return enrichedDocs;
		}

		// Check if MGV aggregation is enabled and applicable
		if (!MgvUtility.shouldAggregateMgvTransactions()) {
			return enrichedDocs;
		}

		// Group MGV transactions by order ID
		Map<String, List<TransformedTransactionHistoryDetail>> mgvGroups = MgvUtility
			.groupMgvTransactionsByOrderId(enrichedDocs.stream().toList());

		Set<TransformedTransactionHistoryDetail> mergedSet = new HashSet<>();
		Set<String> processedOrderIds = new HashSet<>();

		for (TransformedTransactionHistoryDetail doc : enrichedDocs) {
			String orderId = getOrderId(doc);

			// If this is an MGV transaction that needs aggregation
			if (mgvGroups.containsKey(orderId) && mgvGroups.get(orderId).size() > 1
					&& !processedOrderIds.contains(orderId)) {

				// Aggregate multiple MGV transactions
				TransformedTransactionHistoryDetail aggregatedDoc = MgvUtility
					.aggregateMgvTransactions(mgvGroups.get(orderId));

				if (aggregatedDoc != null) {
					setMergeFlag(aggregatedDoc);
					mergedSet.add(aggregatedDoc);
					processedOrderIds.add(orderId);

					log.info("Aggregated {} MGV transactions for orderId: {}", mgvGroups.get(orderId).size(), orderId);
				}
			}
			else if (!mgvGroups.containsKey(orderId) || mgvGroups.get(orderId).size() == 1) {
				// Single transaction or non-MGV transaction
				mergedSet.add(doc);
			}
		}

		return mergedSet;
	}

	@Override
	public UpdateRequest createUpdateRequest(final TransformedTransactionHistoryDetail tthd)
			throws JsonProcessingException {

		if (tthd == null || tthd.getContextMap() == null || tthd.getContextMap().get(IS_MERGED_DOCUMENT) == null) {
			return null;
		}

		UpdateRequest updateRequest = new UpdateRequest();
		updateRequest.index(esRepository.getIndexName(tthd));
		updateRequest.type("_doc");
		updateRequest.id(tthd.docId());
		updateRequest.routing(tthd.getEntityId());

		Map<String, Object> parameters = getParametersForMgvAggregation(tthd);
		if (parameters == null) {
			return null;
		}

		String scriptString = getScriptForMgvAggregation(parameters);
		Script script = new Script(ScriptType.INLINE, "painless", scriptString, parameters);

		log.info("MGV aggregation script: {} for docId: {}", script, tthd.docId());
		updateRequest.script(script);

		return updateRequest;
	}

	/**
	 * Sets the merge flag for aggregated MGV transaction
	 */
	public void setMergeFlag(final TransformedTransactionHistoryDetail tthd) {
		if (tthd.getContextMap() == null) {
			tthd.setContextMap(new HashMap<>());
		}
		tthd.getContextMap().put(IS_MERGED_DOCUMENT, TRUE);
	}

	/**
	 * Gets the order ID from transaction context or system ID
	 */
	private String getOrderId(final TransformedTransactionHistoryDetail txn) {
		if (txn.getContextMap() != null && txn.getContextMap().containsKey("orderId")) {
			return txn.getContextMap().get("orderId");
		}
		if (txn.getContextMap() != null && txn.getContextMap().containsKey("merchantOrderId")) {
			return txn.getContextMap().get("merchantOrderId");
		}
		return txn.getTxnId(); // Fallback to transaction ID
	}

	/**
	 * Creates parameters for MGV aggregation update script
	 */
	private Map<String, Object> getParametersForMgvAggregation(final TransformedTransactionHistoryDetail tthd) {
		Map<String, Object> parameters = new HashMap<>();

		parameters.put("docUpdatedDate", System.currentTimeMillis());
		parameters.put("isMergedDocument", TRUE);

		if (tthd.getContextMap() != null) {
			if (tthd.getContextMap().containsKey("mgv_aggregated")) {
				parameters.put("mgvAggregated", tthd.getContextMap().get("mgv_aggregated"));
			}
			if (tthd.getContextMap().containsKey("mgv_voucher_count")) {
				parameters.put("mgvVoucherCount", tthd.getContextMap().get("mgv_voucher_count"));
			}
		}

		return parameters;
	}

	/**
	 * Creates the update script for MGV aggregation
	 */
	private String getScriptForMgvAggregation(final Map<String, Object> parameters) {
		StringBuilder scriptBuilder = new StringBuilder();

		scriptBuilder.append("ctx._source.docUpdatedDate = params.docUpdatedDate;");
		scriptBuilder.append("if (ctx._source.contextMap == null) {")
			.append("ctx._source.contextMap = ['isMergedDocument' : params.isMergedDocument];")
			.append("} else {")
			.append("ctx._source.contextMap.isMergedDocument = params.isMergedDocument;")
			.append("}");

		if (parameters.containsKey("mgvAggregated")) {
			scriptBuilder.append("ctx._source.contextMap.mgv_aggregated = params.mgvAggregated;");
		}

		if (parameters.containsKey("mgvVoucherCount")) {
			scriptBuilder.append("ctx._source.contextMap.mgv_voucher_count = params.mgvVoucherCount;");
		}

		return scriptBuilder.toString();
	}

	/**
	 * Checks if the document needs MGV aggregation merging
	 */
	public boolean needDocForMerging(final TransformedTransactionHistoryDetail thd) {
		return MgvUtility.isMgvTransaction(thd) && MgvUtility.isMgvRedemption(thd);
	}

}
