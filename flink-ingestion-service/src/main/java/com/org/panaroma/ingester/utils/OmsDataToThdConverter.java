package com.org.panaroma.ingester.utils;

import static com.org.panaroma.commons.constants.CartConstants.GV;
import static com.org.panaroma.commons.constants.CartConstants.ONDC;
import static com.org.panaroma.commons.constants.CartConstants.ONLINE_DEALS;
import static com.org.panaroma.commons.constants.WebConstants.yyyy_MM_dd_T_HH_mm_ss_fff_Z;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.org.panaroma.commons.constants.CartConstants;
import com.org.panaroma.commons.dto.BankData;
import com.org.panaroma.commons.dto.CardData;
import com.org.panaroma.commons.dto.CardType;
import com.org.panaroma.commons.dto.Currency;
import com.org.panaroma.commons.dto.EntityTypesEnum;
import com.org.panaroma.commons.dto.MerchantData;
import com.org.panaroma.commons.dto.MerchantTypeEnum;
import com.org.panaroma.commons.dto.PayModeEnum;
import com.org.panaroma.commons.dto.PaymentSystemEnum;
import com.org.panaroma.commons.dto.TransactionHistoryDetails;
import com.org.panaroma.commons.dto.TransactionSource;
import com.org.panaroma.commons.dto.TxnParticipants;
import com.org.panaroma.commons.dto.cart.CartDetails;
import com.org.panaroma.commons.dto.cart.CartItems;
import com.org.panaroma.commons.dto.cart.CartMetaData;
import com.org.panaroma.commons.dto.cart.CartRespItems;
import com.org.panaroma.commons.enums.OmsPaymentMethodEnum;
import static com.org.panaroma.commons.constants.WebConstants.MGV_PURCHASED;
import static com.org.panaroma.commons.constants.WebConstants.TXN_PURPOSE;
import com.org.panaroma.commons.enums.OmsOrderStatusEnum;
import com.org.panaroma.commons.enums.OmsPaymentKind;
import com.org.panaroma.commons.enums.OmsPaymentStatusEnum;
import com.org.panaroma.commons.utils.CommonsUtility;
import com.org.panaroma.commons.utils.DateTimeUtility;
import com.org.panaroma.commons.utils.Utility;
import com.org.panaroma.ingester.dto.OmsDto;
import com.org.panaroma.ingester.dto.OmsTransactionResponse;
import com.org.panaroma.ingester.dto.PaymentDetails;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TimeZone;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Log4j2
public class OmsDataToThdConverter implements Serializable {

	private List<String> whitelistedMerchantVerticalIds;

	private String onlineDealsVerticalId;

	private String onlineDealsLogoUrl;

	private String gvVerticalId;

	private String gvLogoUrl;

	private String offlineDealsVerticalId;

	private String offlineDealsLogoUrl;

	private List<String> ondcVerticalIds;

	private String ondcLogoUrl;

	private String merchantBaseUrl;

	private Map<String, String> verticalIdToMerchantLogoMap;

	private Utility utility;

	private ObjectMapper objectMapper;

	@Autowired
	public OmsDataToThdConverter(
			@Value("${cart.whitelistedMerchantVerticalIds}") final List<String> whitelistedMerchantVerticalIds,
			@Value("${online.deals.verticalId}") final String onlineDealsVerticalId,
			@Value("${online.deals.logoUrl}") final String onlineDealsLogoUrl,
			@Value("${gv.verticalId}") final String gvVerticalId, @Value("${gv.logoUrl}") final String gvLogoUrl,
			@Value("${offline.deals.verticalId}") final String offlineDealsVerticalId,
			@Value("${offline.deals.logoUrl}") final String offlineDealsLogoUrl,
			@Value("${url.for.ondc.logo}") final String ondcLogoUrl,
			@Value("${ondc.verticalId}") final List<String> ondcVerticalIds,
			@Value("${base.url.for.merchant.logo}") final String merchantBaseUrl, final Utility utility,
			final ObjectMapper objectMapper) {
		this.whitelistedMerchantVerticalIds = whitelistedMerchantVerticalIds;
		this.onlineDealsVerticalId = onlineDealsVerticalId;
		this.onlineDealsLogoUrl = onlineDealsLogoUrl;
		this.gvVerticalId = gvVerticalId;
		this.gvLogoUrl = gvLogoUrl;
		this.offlineDealsVerticalId = offlineDealsVerticalId;
		this.offlineDealsLogoUrl = offlineDealsLogoUrl;
		this.ondcLogoUrl = ondcLogoUrl;
		this.ondcVerticalIds = ondcVerticalIds;
		this.merchantBaseUrl = merchantBaseUrl;
		this.utility = utility;
		this.objectMapper = objectMapper;
		updateVerticalIdToMerchantLogoMap();
	}

	private void updateVerticalIdToMerchantLogoMap() {
		Map<String, String> verticalIdToMerchantLogoMapping = new HashMap<>();

		verticalIdToMerchantLogoMapping.put(ondcVerticalIds.get(0), ondcLogoUrl);
		verticalIdToMerchantLogoMapping.put(offlineDealsVerticalId, offlineDealsLogoUrl);
		verticalIdToMerchantLogoMap = Collections.unmodifiableMap(verticalIdToMerchantLogoMapping);
	}

	public Set<TransactionHistoryDetails> mapOmsDataToThd(final OmsDto omsDto, final Integer paymentKind) {
		if (Objects.isNull(omsDto)) {
			return null;
		}

		Map<OmsPaymentKind, List<PaymentDetails>> omsPaymentKindListMap = new HashMap<>();

		for (PaymentDetails paymentDetails : omsDto.getPayments()) {
			OmsPaymentKind omsPaymentKind = OmsPaymentKind.getOmsPaymentKindByKey(paymentDetails.getKind());

			if (Objects.nonNull(omsPaymentKind)) {
				List<PaymentDetails> paymentDetailsList = Objects.isNull(omsPaymentKindListMap.get(omsPaymentKind))
						? new ArrayList<>() : omsPaymentKindListMap.get(omsPaymentKind);
				paymentDetailsList.add(paymentDetails);
				omsPaymentKindListMap.put(omsPaymentKind, paymentDetailsList);
			}
		}

		Set<TransactionHistoryDetails> thdSet = new HashSet<>();

		for (Map.Entry entry : omsPaymentKindListMap.entrySet()) {
			thdSet.addAll(getTransactHistoryDetailsSet(omsDto, (OmsPaymentKind) entry.getKey(), omsPaymentKindListMap));

		}
		return thdSet;
	}

	Set<TransactionHistoryDetails> getTransactHistoryDetailsSet(final OmsDto omsDto, final OmsPaymentKind paymentKind,
			final Map<OmsPaymentKind, List<PaymentDetails>> omsPaymentKindListMap) {
		Set<TransactionHistoryDetails> transactionHistoryDetailsSet = new HashSet<>();
		if (OmsPaymentKind.FORWARD.equals(paymentKind)) {
			TransactionHistoryDetails thd = new TransactionHistoryDetails();
			thd.setStreamSource(TransactionSource.OMS);
			thd.setAmount(
					StringUtils.isNotBlank(omsDto.getGrandtotal()) ? String.valueOf(omsDto.getGrandtotal()) : null);
			thd.setCurrency(Currency.INR);
			thd.setStatus(OmsOrderStatusEnum.getStatusEnumByKey(omsDto.getPaymentStatus()).getUthMappedStatus());
			thd.setSystemId(omsDto.getOrderId());
			thd.setTxnDate(DateTimeUtility
				.getEpochMillis(omsDto.getCreatedAt(), yyyy_MM_dd_T_HH_mm_ss_fff_Z, TimeZone.getTimeZone("GMT"))
				.toString());
			thd.setUpdatedDate(DateTimeUtility
				.getEpochMillis(omsDto.getUpdatedAt(), yyyy_MM_dd_T_HH_mm_ss_fff_Z, TimeZone.getTimeZone("GMT"))
				.toString());
			thd.setTxnType(OmsPaymentKind.FORWARD.getUthMappedTxnType());
			Map<String, String> contextMap = new HashMap<>();
			contextMap.put("merchantOrderId", String.valueOf(omsDto.getOrderId()));
			// contextMap.put("rrn", omsDto.getPayments().get(0).getBankTransactionId());
			thd.setContextMap(contextMap);

			thd.setCartDetails(getCartDetails(omsDto));

			thd.setParticipants(getParticipantsForForwardTxn(omsDto, omsPaymentKindListMap.get(paymentKind), thd));

			// PTH-397
			if (thd.getCartDetails() != null) {
				thd.getCartDetails().setIsCartRespModified(null);
				thd.getCartDetails().setCustomerId(null);
				thd.getCartDetails().setOrderId(null);
				for (CartItems cartItem : thd.getCartDetails().getItems()) {
					cartItem.setMerchantId(null);
					cartItem.setProductId(null);
				}
			}

			transactionHistoryDetailsSet.add(thd);
		}
		return transactionHistoryDetailsSet;
	}

	private CartDetails getCartDetails(final OmsDto omsDto) {
		CartRespItems cartRespItem = getUthUsedCartItemForCartDetail(omsDto);

		if (Objects.nonNull(cartRespItem)) {
			CartDetails cartDetails = new CartDetails();
			// PTH-397
			cartDetails.setOrderId(omsDto.getOrderId());

			CartItems cartItem = new CartItems();
			// cartItem.setProductId(cartRespItem.getProductId());
			cartItem.setMerchantId(cartRespItem.getMerchantId());
			cartItem.setName(cartRespItem.getName());
			cartItem.setVerticalId(cartRespItem.getVerticalId());
			cartItem.setProductId(cartRespItem.getProductId());

			CartMetaData cartMetaData = utility.getCartMetaData(cartRespItem, omsDto.getOrderId());
			if (Objects.nonNull(cartMetaData)) {
				cartItem.setShopId(cartMetaData.getShopId());
				cartItem.setUthNarration(cartMetaData.getUthNarration());
				cartItem.setUthSubVerticalId(cartMetaData.getUthSubVerticalId());
				cartItem.setProductId(cartRespItem.getProductId());
			}

			cartDetails.setItems(Arrays.asList(cartItem));
			return cartDetails;
		}

		return null;
	}

	private List<TxnParticipants> getParticipantsForForwardTxn(final OmsDto omsDto,
			final List<PaymentDetails> paymentDetailsList, final TransactionHistoryDetails thd) {
		List<TxnParticipants> txnParticipants = new ArrayList<>();

		String mid = null;
		for (PaymentDetails paymentDetails : paymentDetailsList) {
			OmsPaymentMethodEnum paymentMethod = OmsPaymentMethodEnum
				.getStatusEnumByValue(paymentDetails.getPaymentMethod());

			TxnParticipants txnParticipant = new TxnParticipants();
			txnParticipant.setEntityType(EntityTypesEnum.USER);
			txnParticipant.setCustomerId(omsDto.getCustomerId());
			txnParticipant.setAmount(paymentDetails.getPgAmount());
			txnParticipant.setCurrency(Currency.INR);
			// Todo: Need to check if we set this payment status or overall order status
			txnParticipant
				.setStatus(OmsPaymentStatusEnum.getStatusEnumByKey(paymentDetails.getStatus()).getUthMappedStatus());
			txnParticipant.setTxnIndicator(OmsPaymentKind.FORWARD.getUserTxnIndicator());
			txnParticipant
				.setPaymentSystem(OmsPaymentMethodEnum.getPthPaymentStatus(paymentDetails.getPaymentMethod()));
			txnParticipant.setPaymentTxnId(paymentDetails.getTransactionNumber());
			String userName = getUserName(omsDto);

			if (StringUtils.isNotBlank(userName)) {
				txnParticipant.setName(userName);
			}

			if (!OmsPaymentMethodEnum.STORE_CASH.equals(paymentMethod)) {
				txnParticipant.setBankData(getBankData(paymentDetails));
			}

			if (OmsPaymentMethodEnum.CC.equals(paymentMethod) || OmsPaymentMethodEnum.DC.equals(paymentMethod)) {
				txnParticipant.setCardData(getCardData(paymentDetails, paymentMethod));
			}

			txnParticipant.setTxnDate(Objects.nonNull(omsDto.getCreatedAt()) ? DateTimeUtility
				.getEpochMillis(omsDto.getCreatedAt(), yyyy_MM_dd_T_HH_mm_ss_fff_Z, TimeZone.getTimeZone("GMT"))
				.toString() : null);
			txnParticipant.setUpdatedDate(Objects.nonNull(omsDto.getUpdatedAt()) ? DateTimeUtility
				.getEpochMillis(omsDto.getUpdatedAt(), yyyy_MM_dd_T_HH_mm_ss_fff_Z, TimeZone.getTimeZone("GMT"))
				.toString() : null);

			Map<String, String> contextMap = new HashMap<>();
			contextMap.put("bankTxnId", paymentDetails.getBankTransactionId());

			// Set MGV purchase context for MGV payment method
			if (OmsPaymentMethodEnum.MGV.equals(paymentMethod)) {
				contextMap.put(TXN_PURPOSE, MGV_PURCHASED);
			}

			txnParticipant.setContextMap(contextMap);

			txnParticipants.add(txnParticipant);

			if (OmsPaymentMethodEnum.UPI.equals(paymentMethod)) {
				try {
					OmsTransactionResponse omsTransactionResponse = objectMapper
						.readValue((String) paymentDetails.getOmsTransactionResponse(), OmsTransactionResponse.class);
					contextMap.put("rrn", omsTransactionResponse.getRrnCode());
					thd.getContextMap().put("rrn", omsTransactionResponse.getRrnCode());
				}
				catch (JsonProcessingException e) {
					log.error("Exception while parsing omsTransactionResponse for orderId: {}, exception: {}",
							omsDto.getOrderId(), CommonsUtility.exceptionFormatter(e));
					throw new RuntimeException(e);
				}
			}

			if (StringUtils.isBlank(mid)) {
				mid = paymentDetails.getMid();
			}
		}

		TxnParticipants merchantParticipant = new TxnParticipants();
		merchantParticipant.setEntityType(EntityTypesEnum.MERCHANT);
		merchantParticipant.setName(getMerchantName(thd.getCartDetails()));
		merchantParticipant.setLogoUrl(getMerchantLogoUrl(thd.getCartDetails()));
		merchantParticipant.setTxnIndicator(OmsPaymentKind.FORWARD.getMerchantTxnIndicator());
		merchantParticipant.setStatus(thd.getStatus());
		merchantParticipant.setMerchantData(getMerchantData(mid));
		merchantParticipant.setPaymentSystem(PaymentSystemEnum.PG);
		merchantParticipant.setAmount(StringUtils.isNotBlank(omsDto.getGrandtotal()) ? omsDto.getGrandtotal() : null);
		merchantParticipant.setTxnDate(Objects.nonNull(omsDto.getCreatedAt()) ? DateTimeUtility
			.getEpochMillis(omsDto.getCreatedAt(), yyyy_MM_dd_T_HH_mm_ss_fff_Z, TimeZone.getTimeZone("GMT"))
			.toString() : null);
		merchantParticipant.setUpdatedDate(Objects.nonNull(omsDto.getUpdatedAt()) ? DateTimeUtility
			.getEpochMillis(omsDto.getUpdatedAt(), yyyy_MM_dd_T_HH_mm_ss_fff_Z, TimeZone.getTimeZone("GMT"))
			.toString() : null);
		merchantParticipant.setPaymentTxnId(omsDto.getOrderId());

		txnParticipants.add(merchantParticipant);
		return txnParticipants;
	}

	private String getUserName(final OmsDto omsDto) {
		String userName = null;
		if (StringUtils.isNotBlank(omsDto.getCustomerFirstname())) {
			userName = omsDto.getCustomerFirstname();

			if (StringUtils.isNotBlank(omsDto.getCustomerLastname())) {
				userName = userName + " " + omsDto.getCustomerLastname();
			}
		}

		return userName;
	}

	private BankData getBankData(final PaymentDetails paymentDetails) {
		BankData bankData = null;
		if (StringUtils.isNotBlank(paymentDetails.getPaymentBank())) {
			bankData = new BankData();
			bankData.setBankName(paymentDetails.getPaymentBank());
		}
		return bankData;
	}

	private CardData getCardData(final PaymentDetails paymentDetails, OmsPaymentMethodEnum paymentMethodEnum) {
		CardData cardData = new CardData();
		if (OmsPaymentMethodEnum.CC.equals(paymentMethodEnum)) {
			cardData.setCardType(CardType.CREDIT);
		}
		else if (OmsPaymentMethodEnum.DC.equals(paymentMethodEnum)) {
			cardData.setCardType(CardType.DEBIT);
		}
		return cardData;
	}

	private MerchantData getMerchantData(final String mid) {
		if (Objects.isNull(mid)) {
			return null;
		}

		MerchantData merchantData = new MerchantData();
		merchantData.setMerchantId(mid);
		merchantData.setMerchantType(MerchantTypeEnum.ONUS);
		merchantData.setMerchantPayMode(PayModeEnum.ONLINE);
		return merchantData;
	}

	private CartRespItems getUthUsedCartItemForCartDetail(final OmsDto omsDto) {
		if (ObjectUtils.isEmpty(omsDto.getItems())) {
			return null;
		}

		omsDto.getItems().sort(new Comparator<CartRespItems>() {
			@Override
			public int compare(CartRespItems o1, CartRespItems o2) {
				try {
					Double o1Price = Double.parseDouble(o1.getPrice());
					Double o2Price = Double.parseDouble(o2.getPrice());

					if (o2Price >= o1Price) {
						return 1;
					}
					else {
						return -1;
					}
				}
				catch (Exception ex) {
					log.warn("Exception while sorting oms items orderId: {}, exception: {}", omsDto.getOrderId(),
							CommonsUtility.exceptionFormatter(ex));
					return 0;
				}
			}
		});

		CartRespItems cartItem = null;
		for (CartRespItems cartRespItems : omsDto.getItems()) {
			CartMetaData cartMetaData = utility.getCartMetaData(cartRespItems, omsDto.getOrderId());
			if (Objects.nonNull(cartMetaData) && StringUtils.isNotBlank(cartMetaData.getUthNarration())) {
				cartItem = cartRespItems;
				break;
			}
		}

		if (Objects.isNull(cartItem)) {
			cartItem = omsDto.getItems().get(0);
		}

		return cartItem;
	}

	private String getMerchantName(final CartDetails cartDetails) {

		if (Objects.isNull(cartDetails)) {
			return "Paytm Merchant";
		}

		CartItems item = cartDetails.getItems().get(0);

		if (StringUtils.isNotBlank(item.getUthNarration())) {
			return item.getUthNarration();
		}

		String name = null;
		if (whitelistedMerchantVerticalIds != null && !whitelistedMerchantVerticalIds.isEmpty()
				&& (whitelistedMerchantVerticalIds.contains("-1")
						|| whitelistedMerchantVerticalIds.contains(item.getVerticalId()))) {
			if (ObjectUtils.isNotEmpty(ondcVerticalIds) && ondcVerticalIds.contains(item.getVerticalId())) {
				item.setName(CartConstants.MERCHANT_NAME.get(ONDC));
			}

			name = item.getName();
		}

		return StringUtils.isBlank(name) ? "Paytm Merchant" : name;
	}

	private String getMerchantLogoUrl(final CartDetails cartDetails) {

		try {

			if (Objects.isNull(cartDetails)) {
				return null;
			}

			CartItems item = cartDetails.getItems().get(0);

			if (gvVerticalId.equals(item.getVerticalId()) && GV.equals(item.getUthSubVerticalId())) {
				return gvLogoUrl;
			}
			else if (onlineDealsVerticalId.equals(item.getVerticalId())
					&& ONLINE_DEALS.equals(item.getUthSubVerticalId())) {
				return onlineDealsLogoUrl;
			}

			if (StringUtils.isNotBlank(verticalIdToMerchantLogoMap.get(item.getVerticalId()))) {
				return verticalIdToMerchantLogoMap.get(item.getVerticalId());
			}
			else {
				// https://<domain>/images/<mid>/<pid>.jpg
				return this.merchantBaseUrl + item.getMerchantId() + "/" + item.getProductId() + ".jpg";
			}

		}
		catch (Exception e) {
			log.error("Exception while creating merchant logo from oms data. : {}",
					CommonsUtility.exceptionFormatter(e));
		}
		return null;
	}

}
