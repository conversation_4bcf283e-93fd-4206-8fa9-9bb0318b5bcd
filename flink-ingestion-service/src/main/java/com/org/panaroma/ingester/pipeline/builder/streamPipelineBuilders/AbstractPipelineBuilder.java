package com.org.panaroma.ingester.pipeline.builder.streamPipelineBuilders;

import static com.org.panaroma.ingester.constants.Constants.FILTER;

import com.org.panaroma.ingester.configuration.flink.FlinkProperties;
import com.org.panaroma.ingester.dto.streamDTO.BaseStreamDto;
import com.org.panaroma.ingester.pipeline.FlinkPipeline;
import com.org.panaroma.ingester.pipeline.interfaces.IPipeline;
import com.org.panaroma.ingester.pipeline.interfaces.IPipelineBuilder;
import com.org.panaroma.ingester.sinks.factory.FlinkSinksFactory;
import com.org.panaroma.ingester.sinks.objects.FlinkRetrySink;
import com.org.panaroma.ingester.sinks.objects.FlinkSink;
import com.org.panaroma.ingester.sources.customSources.BoPanelSource;
import com.org.panaroma.ingester.sources.factory.FlinkSourcesFactory;
import com.org.panaroma.ingester.sources.objects.FlinkSource;
import com.org.panaroma.ingester.stats.SimpleCounter;
import com.org.panaroma.ingester.switchAdaptor.AbstractSwitchAdaptor;
import com.org.panaroma.ingester.transformer.streamTransformer.AbstractTransformationStep;
import com.org.panaroma.ingester.transformer.streamTransformer.DefaultTransformer;
import com.org.panaroma.ingester.utils.Utility;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.api.datastream.BroadcastConnectedStream;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.streaming.api.functions.source.SourceFunction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;

@Log4j2
public abstract class AbstractPipelineBuilder<I, T> implements IPipelineBuilder {

	private static final String RETRY = "RETRY-";

	private static final int MAX_NUMBER_TRANSFORMATION = 5;

	private static final int MAX_RETRY_COUNT_FOR_EVENT = 5;

	@Autowired
	private FlinkSinksFactory sinkFunctionFactory;

	@Autowired
	private FlinkSourcesFactory sourceFunctionFactory;

	@Autowired
	private FlinkProperties flinkProperties;

	@Autowired
	private BoPanelSource configSource;

	@Value("${default-retry-count-for-events-enabled}")
	private boolean useDefaultEventRetryCount;

	MapStateDescriptor<String, Object> ruleStateDescriptor = new MapStateDescriptor<>("configBroadcastState",
			BasicTypeInfo.STRING_TYPE_INFO, TypeInformation.of(new TypeHint<Object>() {
			}));

	protected AbstractPipelineBuilder() {
	}

	private final Map<String, Object> config = new HashMap<>();

	@Override
	public final IPipeline build(final String pipelineName) {
		log.warn("starting building pipeline {}", pipelineName);
		StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

		setPipelineCheckPointing(pipelineName, env);

		setParallelism(pipelineName, env);

		DataStream inputStream = getInputStream(pipelineName, env);

		Map<String, DataStream> dataStreamMapAfterEachTransformation = getTransformedStringDataStreamMap(pipelineName,
				inputStream);

		BroadcastStream configStream = env.addSource(configSource).broadcast(ruleStateDescriptor);

		pushDataToSink(dataStreamMapAfterEachTransformation, pipelineName, configStream);

		return new FlinkPipeline(env, pipelineName);
	}

	public Map<String, DataStream> getTransformedStringDataStreamMap(final String pipelineName,
			final DataStream<I> inputStream) {
		DataStream streamingInput = getStreamingInput(inputStream);
		AbstractTransformationStep step = getFirstTransformerByPipeline(pipelineName);
		Map<String, DataStream> dataStreamMapAfterEachTransformation = performTransformation(pipelineName, step,
				streamingInput);
		return dataStreamMapAfterEachTransformation;
	}

	// TODO logging size and other info
	protected final void pushDataInRetrySink(final Map<String, DataStream> dataStreamMapAfterEachTransformation,
			final String pipelineName) {
		FlinkRetrySink retrySink = getRetrySink(pipelineName);
		int maxRetryCount = useDefaultEventRetryCount ? MAX_RETRY_COUNT_FOR_EVENT : getMaxRetryCountForEvent();
		if (retrySink != null && retrySink.getSink() != null) {
			dataStreamMapAfterEachTransformation.forEach((s, dataStream) -> {
				if (s.startsWith(RETRY)) {
					DataStream retrySinkStream = dataStream.filter(new FilterFunction<BaseStreamDto>() {

						@Override
						public boolean filter(final BaseStreamDto streamDto) throws Exception {
							return (maxRetryCount == -1 || (streamDto.getRetryCount() < maxRetryCount));
						}
					});
					retrySinkStream = retrySinkStream.map(getOutputMapper()).returns(getRetryDtoTypeInformation());
					retrySinkStream.addSink(retrySink.getSink());
				}
			});
		}
	}

	protected FlinkRetrySink getRetrySink(final String pipelineName) {
		return sinkFunctionFactory.getRetrySinkByPipelineName(pipelineName);
	}

	protected abstract MapFunction getOutputMapper();

	protected DataStream getStreamingInput(final DataStream<I> inputStream) {
		return inputStream.map(getStreamInputMapper());
	}

	protected abstract MapFunction<I, T> getStreamInputMapper();

	protected void pushDataToSink(final Map<String, DataStream> dataStreamMap, final String pipelineName,
			final BroadcastStream configStream) {
		Set<AbstractSwitchAdaptor> switchAdaptors = getSwitchAdaptorByPipelineName(pipelineName);

		switchAdaptors.forEach(switchAdaptor -> {
			DataStream switchStream = switchAdaptor.getApplicableStream(dataStreamMap, pipelineName);
			// Do not override stream reference since we required this reference for other
			// switch as well

			DataStream filteredStream;
			if (switchAdaptor.isFilterApplicable(pipelineName)) {
				filteredStream = switchStream.filter(switchAdaptor.getFilter(pipelineName))
					.name(switchAdaptor.getSwitchName() + FILTER);
			}
			else {
				filteredStream = switchStream;
			}
			DataStream mappedStream;
			if (switchAdaptor.isMappingRequired(pipelineName)) {
				if (switchAdaptor.isConfigRequired()) {
					mappedStream = performMappingWithConfig(pipelineName, filteredStream, configStream, switchAdaptor);
				}
				else {
					mappedStream = performMapping(pipelineName, filteredStream, switchAdaptor);
				}
			}
			else {
				mappedStream = filteredStream;
			}
			putUnSuccessStreamInMap(switchAdaptor.getSwitchName(), mappedStream, dataStreamMap);

			/***
			 * not storing success stream map, since we do not require it after it is
			 * sinked
			 */
			DataStream successStream = getSuccessDtoDataStream(mappedStream);

			List<SinkFunction> sinkList = getSinkFunctionsAdaptor(pipelineName, switchAdaptor.getSwitchName());
			switchAdaptor.applySink(successStream, sinkList, pipelineName);
		});

		pushDataInRetrySink(dataStreamMap, pipelineName);
	}

	private DataStream performMappingWithConfig(final String pipelineName, final DataStream filteredStream,
			final BroadcastStream<Map<String, Object>> configStream, final AbstractSwitchAdaptor switchAdaptor) {
		// connecting config stream to main streams
		BroadcastConnectedStream connect = filteredStream.connect(configStream);
		return connect.process(switchAdaptor)
			.uid(pipelineName + "-" + switchAdaptor.getSwitchName())
			.name(pipelineName + "-" + switchAdaptor.getSwitchName());
	}

	private DataStream performMapping(final String pipelineName, final DataStream filteredStream,
			final AbstractSwitchAdaptor switchAdaptor) {
		return filteredStream.flatMap(switchAdaptor)
			.uid(pipelineName + "-" + switchAdaptor.getSwitchName())
			.name(pipelineName + "-" + switchAdaptor.getSwitchName());
	}

	protected abstract Set<AbstractSwitchAdaptor> getSwitchAdaptorByPipelineName(final String pipelineName);

	protected List<SinkFunction> getSinkFunctionsAdaptor(final String pipeline, final String adaptorName) {
		if (!CollectionUtils.isEmpty(sinkFunctionFactory.getSinksBySwitchAdaptorName(pipeline, adaptorName))) {
			return sinkFunctionFactory.getSinksBySwitchAdaptorName(pipeline, adaptorName)
				.stream()
				.map(FlinkSink::getSink)
				.collect(Collectors.toList());
		}
		return null;
	}

	/***
	 * TODO add infinite loop checks
	 * @param pipelineName pipelineName
	 * @param passedInputStream this is streaming dataStream
	 * @return
	 */
	protected Map<String, DataStream> performTransformation(final String pipelineName,
			final AbstractTransformationStep firstStep, final DataStream passedInputStream) {
		DataStream inputStream = passedInputStream;

		Map<String, DataStream> dataStreamMap = new HashMap<>();
		dataStreamMap.put(DefaultTransformer.TRANSFORMATION_NAME, passedInputStream);

		AbstractTransformationStep step = firstStep;

		int maxNumberOfTransformation = getMaxNumberOfTransformationByPipelineName(pipelineName);
		int currentIteration = 0;
		while (!ObjectUtils.isEmpty(step) && step.isTransformationApplicable(pipelineName)
				&& currentIteration < maxNumberOfTransformation) {
			// TODO Ayansh exception handling in filter
			inputStream = applyFilter(pipelineName, step, inputStream);

			inputStream = getTransformedStream(pipelineName, inputStream, step);
			// update map with transformed success and unsuccessful stream
			putSuccessStreamInMap(step.getTransformationName(), inputStream, dataStreamMap);
			putUnSuccessStreamInMap(step.getTransformationName(), inputStream, dataStreamMap);

			// update inputStream map to successful transformed input stream
			inputStream = dataStreamMap.get(step.getTransformationName());

			// do not remove this line, update step reference with next transformation
			step = step.nextTransformationStep(pipelineName);

			// to avoid infinite loop
			currentIteration++;
		}
		// TODO what type of exception this should be? checked or runtime
		if (currentIteration > maxNumberOfTransformation) {
			log.error("Max Transformation limit breached for pipeline: {}", pipelineName);
			throw new RuntimeException("MaxTransformation breached");
		}
		return dataStreamMap;
	}

	// TODO Default, do we need this value for each pipeline specific or we can set as a
	// global
	protected int getMaxNumberOfTransformationByPipelineName(final String pipelineName) {
		return MAX_NUMBER_TRANSFORMATION;
	}

	private void putUnSuccessStreamInMap(final String operationName, final DataStream inputStream,
			final Map<String, DataStream> dataStreamMap) {
		DataStream retryStream = getRetryDtoDataStream(inputStream);
		dataStreamMap.put("RETRY-" + operationName, retryStream);
	}

	private SingleOutputStreamOperator getRetryDtoDataStream(final DataStream inputStream) {
		return inputStream.filter(new FilterFunction<BaseStreamDto>() {
			@Override
			public boolean filter(final BaseStreamDto baseStreamDto) throws Exception {
				return !baseStreamDto.isSuccess();
			}
		});
	}

	private void putSuccessStreamInMap(final String transformationName, final DataStream inputStream,
			final Map<String, DataStream> dataStreamMap) {
		DataStream successStream = getSuccessDtoDataStream(inputStream);
		dataStreamMap.put(transformationName, successStream);
	}

	private DataStream getSuccessDtoDataStream(final DataStream inputStream) {
		return inputStream.filter(new FilterFunction<BaseStreamDto>() {
			@Override
			public boolean filter(final BaseStreamDto baseStreamDto) throws Exception {
				return baseStreamDto.isSuccess();
			}
		});
	}

	private DataStream getTransformedStream(final String pipelineName, final DataStream passedInputStream,
			final AbstractTransformationStep step) {

		return passedInputStream.flatMap(step)
			.uid(pipelineName + "-" + step.getTransformationName())
			.name(pipelineName + "-" + step.getTransformationName());
	}

	protected DataStream applyFilter(final String pipelineName, final AbstractTransformationStep step,
			final DataStream passedInputStream) {
		DataStream inputStream = passedInputStream;
		inputStream = step.isTransformationFilterApplicable()
				? inputStream.filter(step.getTransformationFilter()).name(step.getTransformationName() + FILTER)
				: inputStream;
		return inputStream;
	}

	protected abstract AbstractTransformationStep getFirstTransformerByPipeline(String pipelineName);

	protected DataStream<I> getInputStream(final String pipelineName, final StreamExecutionEnvironment env) {
		List<SourceFunction> sf = sourceFunctionFactory.getSourceFunctionsByPipeline(pipelineName)
			.stream()
			.map(FlinkSource::getSource)
			.collect(Collectors.toList());
		DataStream inputStream = null;
		for (SourceFunction sourceFunction : sf) {
			DataStream tempStream = env.addSource(sourceFunction)
				.uid(pipelineName + "-source")
				.name(pipelineName + "-source");
			if (inputStream == null) {
				inputStream = tempStream;
			}
			else {
				inputStream.union(tempStream);
			}
		}

		if (inputStream == null) {
			log.error("No SourceFunction is defined for pipeline: {}", pipelineName);
			throw new RuntimeException("No SourceFunction is defined for pipeline: " + pipelineName);
		}

		inputStream.map(new SimpleCounter(pipelineName + "-inputCount"));
		return inputStream;
	}

	private void setPipelineCheckPointing(final String pipelineName, final StreamExecutionEnvironment env) {
		Utility.setCheckPointing(env, flinkProperties, pipelineName);
	}

	private void setParallelism(final String pipelineName, final StreamExecutionEnvironment env) {
		Utility.setParallelism(pipelineName, env, flinkProperties);
	}

	protected abstract TypeInformation getRetryDtoTypeInformation();

	protected int getMaxRetryCountForEvent() {
		return MAX_RETRY_COUNT_FOR_EVENT;
	}

}
